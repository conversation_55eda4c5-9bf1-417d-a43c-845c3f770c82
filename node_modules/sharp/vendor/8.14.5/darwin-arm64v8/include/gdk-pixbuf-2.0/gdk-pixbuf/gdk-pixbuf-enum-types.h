
/* This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. */

#if defined(GDK_PIXBUF_DISABLE_SINGLE_INCLUDES) && !defined (GDK_PIXBUF_H_INSIDE) && !defined (GDK_PIXBUF_COMPILATION)
#error "Only <gdk-pixbuf/gdk-pixbuf.h> can be included directly."
#endif

#ifndef __GDK_PIXBUF_ENUM_TYPES_H__
#define __GDK_PIXBUF_ENUM_TYPES_H__

#include <glib-object.h>

#include <gdk-pixbuf/gdk-pixbuf-macros.h>

G_BEGIN_DECLS

/* enumerations from "gdk-pixbuf-core.h" */
GDK_PIXBUF_AVAILABLE_IN_ALL
GType gdk_pixbuf_alpha_mode_get_type (void) G_GNUC_CONST;
#define GDK_TYPE_PIXBUF_ALPHA_MODE (gdk_pixbuf_alpha_mode_get_type ())
GDK_PIXBUF_AVAILABLE_IN_ALL
GType gdk_colorspace_get_type (void) G_GNUC_CONST;
#define GDK_TYPE_COLORSPACE (gdk_colorspace_get_type ())
GDK_PIXBUF_AVAILABLE_IN_ALL
GType gdk_pixbuf_error_get_type (void) G_GNUC_CONST;
#define GDK_TYPE_PIXBUF_ERROR (gdk_pixbuf_error_get_type ())

/* enumerations from "gdk-pixbuf-transform.h" */
GDK_PIXBUF_AVAILABLE_IN_ALL
GType gdk_interp_type_get_type (void) G_GNUC_CONST;
#define GDK_TYPE_INTERP_TYPE (gdk_interp_type_get_type ())
GDK_PIXBUF_AVAILABLE_IN_ALL
GType gdk_pixbuf_rotation_get_type (void) G_GNUC_CONST;
#define GDK_TYPE_PIXBUF_ROTATION (gdk_pixbuf_rotation_get_type ())
G_END_DECLS

#endif /* __GDK_PIXBUF_ENUM_TYPES_H__ */

/* Generated data ends here */

