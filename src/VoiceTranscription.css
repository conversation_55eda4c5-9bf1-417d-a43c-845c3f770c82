.voice-transcription {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
}

.header p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.5;
}

.network-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 1rem;
  color: #856404;
  font-weight: 600;
  text-align: center;
}

.controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.mic-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  font-size: 1rem;
  font-weight: 600;
}

.mic-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.mic-button:active {
  transform: translateY(0);
}

.mic-button.listening {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  animation: pulse 1.5s infinite;
}

.mic-button.offline {
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
  cursor: not-allowed;
  opacity: 0.6;
}

.mic-button.offline:hover {
  transform: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4), 0 0 0 10px rgba(255, 107, 107, 0.1);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

.mic-icon {
  font-size: 2rem;
  margin-bottom: 0.25rem;
}

.mic-text {
  font-size: 0.9rem;
  text-align: center;
  line-height: 1.2;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.clear-button,
.copy-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.clear-button {
  background-color: #ff6b6b;
  color: white;
}

.clear-button:hover {
  background-color: #ff5252;
  transform: translateY(-1px);
}

.copy-button {
  background-color: #4ecdc4;
  color: white;
}

.copy-button:hover {
  background-color: #26d0ce;
  transform: translateY(-1px);
}

.test-connection {
  margin-top: 1rem;
}

.test-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  background-color: #1976d2;
  color: white;
}

.test-button:hover {
  background-color: #1565c0;
  transform: translateY(-1px);
}

.fallback-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  background-color: #f39c12;
  color: white;
  width: 100%;
}

.fallback-button:hover {
  background-color: #e67e22;
  transform: translateY(-1px);
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  text-align: center;
}

.error-message h2 {
  color: #c62828;
  margin-bottom: 0.5rem;
}

.error-message p {
  color: #d32f2f;
  margin: 0;
}

.retry-info {
  color: #1976d2 !important;
  font-size: 0.9rem;
  margin-top: 0.5rem !important;
  font-style: italic;
}

.troubleshooting {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.troubleshooting h4 {
  color: #1976d2;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.troubleshooting ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
  text-align: left;
}

.troubleshooting li {
  margin: 0.25rem 0;
  color: #555;
  font-size: 0.9rem;
}

.troubleshooting p {
  margin: 0.75rem 0 0 0;
  font-size: 0.85rem;
  color: #666 !important;
  font-style: italic;
}

.transcript-container {
  margin-bottom: 2rem;
}

.transcript-box {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  background-color: #fafafa;
  font-size: 1.1rem;
  line-height: 1.6;
}

.final-transcript {
  color: #333;
  margin-bottom: 0.5rem;
}

.interim-transcript {
  color: #888;
  font-style: italic;
}

.placeholder {
  color: #aaa;
  text-align: center;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 150px;
}

.listening-indicator {
  color: #ff6b6b;
  text-align: center;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 150px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

.status {
  text-align: center;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  background-color: #f5f5f5;
  color: #666;
  transition: all 0.3s ease;
}

.status-indicator.active {
  background-color: #ffebee;
  color: #c62828;
}

/* Responsive design */
@media (max-width: 768px) {
  .voice-transcription {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .mic-button {
    width: 100px;
    height: 100px;
  }
  
  .mic-icon {
    font-size: 1.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .clear-button,
  .copy-button {
    width: 100%;
  }
}
