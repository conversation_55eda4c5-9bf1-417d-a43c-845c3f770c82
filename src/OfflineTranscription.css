.offline-transcription {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
}

.header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  line-height: 1.5;
}

.model-loading {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #e8f4fd;
  border-radius: 8px;
  border: 1px solid #bee5eb;
}

.loading-bar {
  width: 100%;
  height: 8px;
  background-color: #dee2e6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.model-loading p {
  margin: 0;
  color: #0c5460;
  font-size: 0.9rem;
}

.controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.mic-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  font-size: 1rem;
  font-weight: 600;
}

.mic-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.mic-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.mic-button.recording {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  animation: pulse-recording 1.5s infinite;
}

.mic-button.transcribing {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  animation: pulse-transcribing 2s infinite;
}

@keyframes pulse-recording {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4), 0 0 0 10px rgba(231, 76, 60, 0.1);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

@keyframes pulse-transcribing {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4), 0 0 0 10px rgba(243, 156, 18, 0.1);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

.mic-icon {
  font-size: 2rem;
  margin-bottom: 0.25rem;
}

.mic-text {
  font-size: 0.9rem;
  text-align: center;
  line-height: 1.2;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.clear-button,
.copy-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.clear-button {
  background-color: #e74c3c;
  color: white;
}

.clear-button:hover {
  background-color: #c0392b;
  transform: translateY(-1px);
}

.copy-button {
  background-color: #27ae60;
  color: white;
}

.copy-button:hover {
  background-color: #229954;
  transform: translateY(-1px);
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  text-align: center;
}

.error-message.info {
  background-color: #e3f2fd;
  border-color: #bbdefb;
}

.error-message p {
  color: #d32f2f;
  margin: 0;
}

.error-message.info p {
  color: #1976d2;
}

.transcript-container {
  margin-bottom: 2rem;
}

.transcript-box {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  background-color: #fafafa;
  font-size: 1.1rem;
  line-height: 1.6;
}

.final-transcript {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.placeholder {
  color: #bdc3c7;
  text-align: center;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 150px;
}

.recording-indicator {
  color: #e74c3c;
  text-align: center;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 150px;
  animation: blink 1s infinite;
}

.transcribing-indicator {
  color: #f39c12;
  text-align: center;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 150px;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.7; }
}

.status {
  text-align: center;
  margin-bottom: 2rem;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.status-indicator.loading {
  background-color: #fff3cd;
  color: #856404;
}

.status-indicator.ready {
  background-color: #d4edda;
  color: #155724;
}

.status-indicator.recording {
  background-color: #f8d7da;
  color: #721c24;
}

.status-indicator.transcribing {
  background-color: #ffeaa7;
  color: #856404;
}

.info-section {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #3498db;
}

.info-section h3 {
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.info-section ul {
  margin: 0;
  padding-left: 1.5rem;
}

.info-section li {
  margin: 0.5rem 0;
  color: #34495e;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .offline-transcription {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .mic-button {
    width: 100px;
    height: 100px;
  }
  
  .mic-icon {
    font-size: 1.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .clear-button,
  .copy-button {
    width: 100%;
  }
}
