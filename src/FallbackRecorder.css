.fallback-recorder {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

.fallback-header {
  text-align: center;
  margin-bottom: 2rem;
}

.fallback-header h2 {
  color: #856404;
  margin-bottom: 0.5rem;
}

.fallback-header p {
  color: #664d03;
  font-size: 1rem;
}

.fallback-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.record-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  font-weight: 600;
}

.record-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.record-button.recording {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  animation: pulse-fallback 1.5s infinite;
}

@keyframes pulse-fallback {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4), 0 0 0 10px rgba(231, 76, 60, 0.1);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

.record-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.record-text {
  font-size: 0.8rem;
  text-align: center;
  line-height: 1.2;
}

.audio-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.audio-controls audio {
  width: 100%;
  max-width: 400px;
}

.audio-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.download-button,
.clear-audio-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.download-button {
  background-color: #27ae60;
  color: white;
}

.download-button:hover {
  background-color: #229954;
  transform: translateY(-1px);
}

.clear-audio-button {
  background-color: #e74c3c;
  color: white;
}

.clear-audio-button:hover {
  background-color: #c0392b;
  transform: translateY(-1px);
}

.fallback-error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  text-align: center;
}

.fallback-error p {
  color: #721c24;
  margin: 0;
}

.fallback-info {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 8px;
}

.fallback-info h3 {
  color: #0c5460;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.fallback-info ul {
  margin: 0;
  padding-left: 1.5rem;
}

.fallback-info li {
  margin: 0.5rem 0;
  color: #0c5460;
  font-size: 0.95rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .fallback-recorder {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .audio-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .download-button,
  .clear-audio-button {
    width: 100%;
  }
}
