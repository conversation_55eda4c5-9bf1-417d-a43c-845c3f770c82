import { useState, useRef, useEffect } from 'react';
import { pipeline } from '@xenova/transformers';
import './OfflineTranscription.css';

const OfflineTranscription = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState('');
  const [modelLoaded, setModelLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const transcribeRef = useRef(null);
  const silenceTimerRef = useRef(null);

  useEffect(() => {
    // Initialize the transcription pipeline
    const initializeModel = async () => {
      try {
        setError('Loading AI model... This may take a few minutes on first use.');
        
        // Create transcription pipeline with progress callback
        transcribeRef.current = await pipeline('automatic-speech-recognition', 'Xenova/whisper-tiny.en', {
          progress_callback: (progress) => {
            if (progress.status === 'downloading') {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              setLoadingProgress(percent);
              setError(`Downloading model... ${percent}%`);
            } else if (progress.status === 'loading') {
              setError('Loading model into memory...');
            }
          }
        });
        
        setModelLoaded(true);
        setError('');
        setLoadingProgress(100);
      } catch (err) {
        setError('Failed to load AI model. Please refresh the page and try again.');
        console.error('Model loading error:', err);
      }
    };

    initializeModel();
  }, []);

  const startRecording = async () => {
    if (!modelLoaded) {
      setError('AI model is still loading. Please wait...');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000, // Whisper prefers 16kHz
          channelCount: 1,   // Mono audio
          echoCancellation: true,
          noiseSuppression: true
        } 
      });
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await transcribeAudio(audioBlob);
        
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);
      setError('');

      // Auto-stop after silence (5 seconds for offline processing)
      resetSilenceTimer();
      
    } catch (err) {
      setError('Microphone access denied. Please allow microphone access and try again.');
      console.error('Recording error:', err);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      clearSilenceTimer();
    }
  };

  const resetSilenceTimer = () => {
    clearSilenceTimer();
    silenceTimerRef.current = setTimeout(() => {
      if (isRecording) {
        stopRecording();
      }
    }, 5000); // 5 seconds of silence
  };

  const clearSilenceTimer = () => {
    if (silenceTimerRef.current) {
      clearTimeout(silenceTimerRef.current);
      silenceTimerRef.current = null;
    }
  };

  const transcribeAudio = async (audioBlob) => {
    if (!transcribeRef.current) {
      setError('Transcription model not loaded');
      return;
    }

    try {
      setIsTranscribing(true);
      setError('Transcribing audio...');

      // Convert blob to audio buffer
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      // Convert to the format expected by Whisper (Float32Array, mono, 16kHz)
      let audioData = audioBuffer.getChannelData(0);
      
      // Resample to 16kHz if necessary
      if (audioBuffer.sampleRate !== 16000) {
        audioData = resampleAudio(audioData, audioBuffer.sampleRate, 16000);
      }

      // Transcribe the audio
      const result = await transcribeRef.current(audioData);
      
      if (result && result.text) {
        setTranscript(prev => prev + result.text + ' ');
        setError('');
      } else {
        setError('No speech detected. Please try speaking louder or closer to the microphone.');
      }
      
    } catch (err) {
      setError('Transcription failed. Please try again.');
      console.error('Transcription error:', err);
    } finally {
      setIsTranscribing(false);
    }
  };

  // Simple audio resampling function
  const resampleAudio = (audioData, originalSampleRate, targetSampleRate) => {
    if (originalSampleRate === targetSampleRate) {
      return audioData;
    }
    
    const ratio = originalSampleRate / targetSampleRate;
    const newLength = Math.round(audioData.length / ratio);
    const result = new Float32Array(newLength);
    
    for (let i = 0; i < newLength; i++) {
      const index = i * ratio;
      const indexFloor = Math.floor(index);
      const indexCeil = Math.min(indexFloor + 1, audioData.length - 1);
      const fraction = index - indexFloor;
      
      result[i] = audioData[indexFloor] * (1 - fraction) + audioData[indexCeil] * fraction;
    }
    
    return result;
  };

  const clearTranscript = () => {
    setTranscript('');
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(transcript);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className="offline-transcription">
      <div className="header">
        <h1>🤖 Offline Voice to Text</h1>
        <p>AI-powered transcription that works completely offline using Whisper AI</p>
        {!modelLoaded && (
          <div className="model-loading">
            <div className="loading-bar">
              <div 
                className="loading-progress" 
                style={{ width: `${loadingProgress}%` }}
              ></div>
            </div>
            <p>Loading AI model... Please wait (this only happens once)</p>
          </div>
        )}
      </div>

      <div className="controls">
        <button
          className={`mic-button ${isRecording ? 'recording' : ''} ${isTranscribing ? 'transcribing' : ''}`}
          onClick={isRecording ? stopRecording : startRecording}
          disabled={!modelLoaded || isTranscribing}
        >
          <span className="mic-icon">
            {isTranscribing ? '🤖' : isRecording ? '⏹️' : '🎤'}
          </span>
          <span className="mic-text">
            {isTranscribing ? 'Transcribing...' : isRecording ? 'Stop Recording' : 'Start Recording'}
          </span>
        </button>

        {transcript && (
          <div className="action-buttons">
            <button onClick={clearTranscript} className="clear-button">
              Clear Text
            </button>
            <button onClick={copyToClipboard} className="copy-button">
              Copy Text
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className={`error-message ${error.includes('Loading') || error.includes('Downloading') ? 'info' : ''}`}>
          <p>{error}</p>
        </div>
      )}

      <div className="transcript-container">
        <div className="transcript-box">
          <div className="final-transcript">
            {transcript}
          </div>
          {!transcript && !isRecording && !isTranscribing && modelLoaded && (
            <div className="placeholder">
              Your transcribed text will appear here...
            </div>
          )}
          {isRecording && (
            <div className="recording-indicator">
              🎙️ Recording... Speak now! (Auto-stops after 5 seconds of silence)
            </div>
          )}
          {isTranscribing && (
            <div className="transcribing-indicator">
              🤖 AI is transcribing your speech...
            </div>
          )}
        </div>
      </div>

      <div className="status">
        <div className={`status-indicator ${isRecording ? 'recording' : isTranscribing ? 'transcribing' : modelLoaded ? 'ready' : 'loading'}`}>
          {isRecording ? '🔴 Recording' : 
           isTranscribing ? '🤖 Transcribing' : 
           modelLoaded ? '✅ Ready (Offline)' : '⏳ Loading Model'}
        </div>
      </div>

      <div className="info-section">
        <h3>✨ Features:</h3>
        <ul>
          <li>🔒 <strong>100% Offline</strong> - No internet required after initial setup</li>
          <li>🤖 <strong>AI Powered</strong> - Uses OpenAI's Whisper model</li>
          <li>🎯 <strong>High Accuracy</strong> - State-of-the-art speech recognition</li>
          <li>🔐 <strong>Privacy First</strong> - Your audio never leaves your device</li>
          <li>⚡ <strong>Auto-Stop</strong> - Automatically stops after silence</li>
        </ul>
      </div>
    </div>
  );
};

export default OfflineTranscription;
