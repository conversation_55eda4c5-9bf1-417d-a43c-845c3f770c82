import { useState, useRef } from 'react';
import './FallbackRecorder.css';

const FallbackRecorder = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioURL, setAudioURL] = useState('');
  const [error, setError] = useState('');
  
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setAudioURL(audioUrl);
        
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      setError('');
    } catch (err) {
      setError('Microphone access denied. Please allow microphone access and try again.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const downloadRecording = () => {
    if (audioURL) {
      const a = document.createElement('a');
      a.href = audioURL;
      a.download = `recording-${new Date().toISOString().slice(0, 19)}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const clearRecording = () => {
    setAudioURL('');
    if (audioURL) {
      URL.revokeObjectURL(audioURL);
    }
  };

  return (
    <div className="fallback-recorder">
      <div className="fallback-header">
        <h2>🎙️ Audio Recorder (Fallback Mode)</h2>
        <p>Speech recognition is not available, but you can still record audio.</p>
      </div>

      <div className="fallback-controls">
        <button
          className={`record-button ${isRecording ? 'recording' : ''}`}
          onClick={isRecording ? stopRecording : startRecording}
        >
          <span className="record-icon">{isRecording ? '⏹️' : '🎤'}</span>
          <span className="record-text">
            {isRecording ? 'Stop Recording' : 'Start Recording'}
          </span>
        </button>

        {audioURL && (
          <div className="audio-controls">
            <audio controls src={audioURL} />
            <div className="audio-buttons">
              <button onClick={downloadRecording} className="download-button">
                Download Recording
              </button>
              <button onClick={clearRecording} className="clear-audio-button">
                Clear Recording
              </button>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="fallback-error">
          <p>{error}</p>
        </div>
      )}

      <div className="fallback-info">
        <h3>Alternative Solutions:</h3>
        <ul>
          <li>Use Google Docs voice typing (docs.google.com)</li>
          <li>Try Windows Speech Recognition (Windows + H)</li>
          <li>Use your phone's voice-to-text feature</li>
          <li>Try a different browser or network connection</li>
        </ul>
      </div>
    </div>
  );
};

export default FallbackRecorder;
