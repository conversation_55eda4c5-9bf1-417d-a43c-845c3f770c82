import { useState, useRef, useEffect } from 'react';
import './VoiceTranscription.css';

const VoiceTranscription = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState('');
  const [isSupported, setIsSupported] = useState(true);
  
  const recognitionRef = useRef(null);
  const silenceTimerRef = useRef(null);

  useEffect(() => {
    // Check if Web Speech API is supported
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      setIsSupported(false);
      setError('Speech recognition is not supported in this browser. Please use Chrome, Edge, or Safari.');
      return;
    }

    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    recognition.maxAlternatives = 1;

    recognition.onstart = () => {
      setIsListening(true);
      setError('');
    };

    recognition.onresult = (event) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      if (finalTranscript) {
        setTranscript(prev => prev + finalTranscript + ' ');
        setInterimTranscript('');
      } else {
        setInterimTranscript(interimTranscript);
      }

      // Reset silence timer on speech
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
      
      // Set new silence timer (3 seconds of silence will stop recording)
      silenceTimerRef.current = setTimeout(() => {
        if (recognitionRef.current && isListening) {
          recognition.stop();
        }
      }, 3000);
    };

    recognition.onerror = (event) => {
      setError(`Speech recognition error: ${event.error}`);
      setIsListening(false);
    };

    recognition.onend = () => {
      setIsListening(false);
      setInterimTranscript('');
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
    };

    recognitionRef.current = recognition;

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
    };
  }, [isListening]);

  const startListening = async () => {
    if (!isSupported) return;

    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });
      
      setError('');
      if (recognitionRef.current) {
        recognitionRef.current.start();
      }
    } catch (err) {
      setError('Microphone access denied. Please allow microphone access and try again.');
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
  };

  const clearTranscript = () => {
    setTranscript('');
    setInterimTranscript('');
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(transcript);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  if (!isSupported) {
    return (
      <div className="voice-transcription">
        <div className="error-message">
          <h2>Speech Recognition Not Supported</h2>
          <p>Your browser doesn't support speech recognition. Please use Chrome, Edge, or Safari.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="voice-transcription">
      <div className="header">
        <h1>🎤 Voice to Text Transcription</h1>
        <p>Click the microphone button and start speaking. Recording will automatically stop after 3 seconds of silence.</p>
      </div>

      <div className="controls">
        <button
          className={`mic-button ${isListening ? 'listening' : ''}`}
          onClick={isListening ? stopListening : startListening}
          disabled={!isSupported}
        >
          <span className="mic-icon">🎤</span>
          <span className="mic-text">
            {isListening ? 'Stop Recording' : 'Start Recording'}
          </span>
        </button>

        {transcript && (
          <div className="action-buttons">
            <button onClick={clearTranscript} className="clear-button">
              Clear Text
            </button>
            <button onClick={copyToClipboard} className="copy-button">
              Copy Text
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="error-message">
          <p>{error}</p>
        </div>
      )}

      <div className="transcript-container">
        <div className="transcript-box">
          <div className="final-transcript">
            {transcript}
          </div>
          {interimTranscript && (
            <div className="interim-transcript">
              {interimTranscript}
            </div>
          )}
          {!transcript && !interimTranscript && !isListening && (
            <div className="placeholder">
              Your transcribed text will appear here...
            </div>
          )}
          {isListening && !interimTranscript && (
            <div className="listening-indicator">
              🎙️ Listening... Speak now!
            </div>
          )}
        </div>
      </div>

      <div className="status">
        <div className={`status-indicator ${isListening ? 'active' : ''}`}>
          {isListening ? '🔴 Recording' : '⚫ Ready'}
        </div>
      </div>
    </div>
  );
};

export default VoiceTranscription;
