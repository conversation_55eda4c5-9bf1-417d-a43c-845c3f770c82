import { useState, useRef, useEffect } from 'react';
import './VoiceTranscription.css';
import FallbackRecorder from './FallbackRecorder';

const VoiceTranscription = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState('');
  const [isSupported, setIsSupported] = useState(true);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [retryCount, setRetryCount] = useState(0);
  const [showFallback, setShowFallback] = useState(false);

  const recognitionRef = useRef(null);
  const silenceTimerRef = useRef(null);
  const maxRetries = 3;

  useEffect(() => {
    // Monitor network connectivity
    const handleOnline = () => {
      setIsOnline(true);
      if (error.includes('internet connection')) {
        setError('');
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setError('No internet connection. Speech recognition requires an active internet connection.');
      if (isListening) {
        stopListening();
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check if Web Speech API is supported
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      setIsSupported(false);
      setError('Speech recognition is not supported in this browser. Please use Chrome, Edge, or Safari.');
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }

    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    recognition.maxAlternatives = 1;

    // Additional configuration to help with network issues
    recognition.grammars = null;
    recognition.serviceURI = null; // Let browser choose the best service

    recognition.onstart = () => {
      setIsListening(true);
      setError('');
    };

    recognition.onresult = (event) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      if (finalTranscript) {
        setTranscript(prev => prev + finalTranscript + ' ');
        setInterimTranscript('');
      } else {
        setInterimTranscript(interimTranscript);
      }

      // Reset silence timer on speech
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
      
      // Set new silence timer (3 seconds of silence will stop recording)
      silenceTimerRef.current = setTimeout(() => {
        if (recognitionRef.current && isListening) {
          recognition.stop();
        }
      }, 3000);
    };

    recognition.onerror = (event) => {
      let errorMessage = '';
      switch (event.error) {
        case 'network':
          errorMessage = 'Network error: Please check your internet connection. Speech recognition requires an active internet connection.';
          break;
        case 'not-allowed':
          errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings and try again.';
          break;
        case 'no-speech':
          errorMessage = 'No speech detected. Please try speaking louder or closer to the microphone.';
          break;
        case 'audio-capture':
          errorMessage = 'Audio capture failed. Please check your microphone connection.';
          break;
        case 'service-not-allowed':
          errorMessage = 'Speech recognition service not allowed. Please enable speech recognition in your browser.';
          break;
        case 'bad-grammar':
          errorMessage = 'Grammar error in speech recognition.';
          break;
        case 'language-not-supported':
          errorMessage = 'Language not supported. Please try switching to English.';
          break;
        default:
          errorMessage = `Speech recognition error: ${event.error}. Please try again.`;
      }
      setError(errorMessage);
      setIsListening(false);

      // Auto-retry for network errors after a delay
      if (event.error === 'network' && retryCount < maxRetries) {
        setTimeout(() => {
          retryListening();
        }, 2000);
      } else if (event.error === 'network') {
        setTimeout(() => {
          setError('Network connection failed after multiple attempts. Please check your internet connection and try again.');
        }, 1000);
      }
    };

    recognition.onend = () => {
      setIsListening(false);
      setInterimTranscript('');
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
    };

    recognitionRef.current = recognition;

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isListening]);

  const startListening = async () => {
    if (!isSupported) return;

    // Check internet connection
    if (!navigator.onLine) {
      setError('No internet connection. Speech recognition requires an active internet connection.');
      return;
    }

    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });

      setError('');
      setRetryCount(0);
      if (recognitionRef.current) {
        recognitionRef.current.start();
      }
    } catch (err) {
      if (err.name === 'NotAllowedError') {
        setError('Microphone access denied. Please allow microphone access and try again.');
      } else if (err.name === 'NotFoundError') {
        setError('No microphone found. Please connect a microphone and try again.');
      } else {
        setError('Error accessing microphone. Please check your microphone settings.');
      }
    }
  };

  const retryListening = () => {
    if (retryCount < maxRetries && navigator.onLine) {
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        startListening();
      }, 2000);
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
  };

  const clearTranscript = () => {
    setTranscript('');
    setInterimTranscript('');
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(transcript);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const testConnection = async () => {
    setError('Testing connection...');

    try {
      // Test basic internet connectivity
      await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      });

      // Test speech recognition initialization
      if (recognitionRef.current) {
        const testRecognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
        testRecognition.lang = 'en-US';
        testRecognition.continuous = false;
        testRecognition.interimResults = false;

        testRecognition.onstart = () => {
          setError('Connection test successful! Speech recognition is working. Try recording again.');
          setTimeout(() => testRecognition.stop(), 1000);
        };

        testRecognition.onerror = (event) => {
          setError(`Connection test failed: ${event.error}. This confirms the network issue with speech recognition services.`);
        };

        testRecognition.onend = () => {
          // Test completed
        };

        testRecognition.start();
      }
    } catch (err) {
      setError('Connection test failed: Unable to reach external services. Please check your internet connection.');
    }
  };

  if (!isSupported) {
    return (
      <div className="voice-transcription">
        <div className="error-message">
          <h2>Speech Recognition Not Supported</h2>
          <p>Your browser doesn't support speech recognition. Please use Chrome, Edge, or Safari.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="voice-transcription">
      <div className="header">
        <h1>🎤 Voice to Text Transcription</h1>
        <p>Click the microphone button and start speaking. Recording will automatically stop after 3 seconds of silence.</p>
        {!isOnline && (
          <div className="network-warning">
            ⚠️ No internet connection - Speech recognition requires internet access
          </div>
        )}
      </div>

      <div className="controls">
        <button
          className={`mic-button ${isListening ? 'listening' : ''} ${!isOnline ? 'offline' : ''}`}
          onClick={isListening ? stopListening : startListening}
          disabled={!isSupported || !isOnline}
        >
          <span className="mic-icon">🎤</span>
          <span className="mic-text">
            {isListening ? 'Stop Recording' : 'Start Recording'}
          </span>
        </button>

        {transcript && (
          <div className="action-buttons">
            <button onClick={clearTranscript} className="clear-button">
              Clear Text
            </button>
            <button onClick={copyToClipboard} className="copy-button">
              Copy Text
            </button>
          </div>
        )}

        {error.includes('Network') && (
          <div className="test-connection">
            <button onClick={testConnection} className="test-button">
              Test Connection
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="error-message">
          <p>{error}</p>
          {retryCount > 0 && retryCount < maxRetries && error.includes('Network') && (
            <p className="retry-info">Retrying... (Attempt {retryCount + 1}/{maxRetries})</p>
          )}
          {error.includes('Network') && (
            <div className="troubleshooting">
              <h4>Troubleshooting Steps:</h4>
              <ul>
                <li>✓ Check your internet connection</li>
                <li>✓ Try refreshing the page</li>
                <li>✓ Ensure you're using Chrome, Edge, or Safari</li>
                <li>✓ Check if your firewall/antivirus is blocking the connection</li>
                <li>✓ Try using a different network (mobile hotspot)</li>
                <li>✓ Clear browser cache and cookies</li>
              </ul>
              <p><strong>Note:</strong> Speech recognition uses Google's servers and may not work on some corporate networks or with certain VPNs.</p>
              <button
                onClick={() => setShowFallback(true)}
                className="fallback-button"
                style={{ marginTop: '1rem' }}
              >
                Use Audio Recorder Instead
              </button>
            </div>
          )}
        </div>
      )}

      {showFallback && <FallbackRecorder />}

      <div className="transcript-container">
        <div className="transcript-box">
          <div className="final-transcript">
            {transcript}
          </div>
          {interimTranscript && (
            <div className="interim-transcript">
              {interimTranscript}
            </div>
          )}
          {!transcript && !interimTranscript && !isListening && (
            <div className="placeholder">
              Your transcribed text will appear here...
            </div>
          )}
          {isListening && !interimTranscript && (
            <div className="listening-indicator">
              🎙️ Listening... Speak now!
            </div>
          )}
        </div>
      </div>

      <div className="status">
        <div className={`status-indicator ${isListening ? 'active' : ''}`}>
          {isListening ? '🔴 Recording' : '⚫ Ready'}
        </div>
      </div>
    </div>
  );
};

export default VoiceTranscription;
